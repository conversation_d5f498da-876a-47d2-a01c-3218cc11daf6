{"name": "local-util-backend", "version": "1.0.0", "description": "本地工具后端服务", "main": "src/main.ts", "exports": "./src/main.ts", "tasks": {"dev": "deno run --allow-net --allow-read --allow-write --allow-env --watch src/main.ts", "start": "deno run --allow-net --allow-read --allow-write --allow-env src/main.ts", "test": "deno test --allow-net --allow-read --allow-write --allow-env", "import-data": "deno run --allow-net --allow-read --allow-write --allow-env import_sample_data.ts"}, "scripts": {"dev": "deno run --allow-net --allow-read --allow-write --allow-env --watch src/main.ts", "start": "deno run --allow-net --allow-read --allow-write --allow-env src/main.ts", "test": "deno test --allow-net --allow-read --allow-write --allow-env", "import-data": "deno run --allow-net --allow-read --allow-write --allow-env import_sample_data.ts"}, "imports": {"oak": "https://deno.land/x/oak@v12.6.1/mod.ts", "mongo": "https://deno.land/x/mongo@v0.32.0/mod.ts", "cors": "https://deno.land/x/oak_cors@v0.1.1/mod.ts"}, "compilerOptions": {"lib": ["deno.window"], "strict": true}, "lint": {"rules": {"tags": ["recommended"]}}, "fmt": {"files": {"include": ["src/"]}, "options": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": true}}}