# 本地工具后端服务 - 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
本地工具后端服务是一个基于AI的HTML代码生成平台，支持通过JSON示例和提示词模板生成高质量的HTML代码。系统采用任务队列机制，确保生成任务的可靠执行和状态管理。

### 1.2 核心功能
- **模板管理**: 管理JSON示例、TypeScript类型定义和提示词模板
- **HTML生成**: 基于AI模型生成HTML代码
- **任务队列**: 异步处理生成任务，支持任务状态跟踪
- **生成记录**: 记录和管理所有生成历史

### 1.3 技术架构
- **后端框架**: Deno + Oak
- **数据库**: MongoDB
- **AI服务**: 支持多种AI模型（GPT-4等）
- **任务队列**: 内存队列 + 数据库持久化

## 2. 数据库设计

### 2.1 模板表 (templates)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | ObjectId | 主键 | MongoDB自动生成的ID |
| jsonExample | String | 必填 | JSON示例数据 |
| tsDefinition | String | 必填，唯一 | TypeScript类型定义 |
| diagramLink | String | 可选 | 示意图链接URL |
| prompt | String | 必填 | 生成提示词 |
| createdAt | Date | 自动 | 创建时间 |
| updatedAt | Date | 自动 | 更新时间 |

**索引设计:**
- `tsDefinition`: 唯一索引，确保类型定义不重复
- `createdAt`: 降序索引，支持按时间排序

### 2.2 生成记录表 (generation_records)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | ObjectId | 主键 | MongoDB自动生成的ID |
| templateId | String | 必填 | 关联模板表的ID |
| generatedResult | String | 必填 | 生成的结果内容 |
| modelUsed | String | 必填 | 使用的AI模型名称 |
| createdAt | Date | 自动 | 创建时间 |
| updatedAt | Date | 自动 | 更新时间 |

**索引设计:**
- `templateId`: 普通索引，支持按模板查询
- `modelUsed`: 普通索引，支持按模型查询
- `createdAt`: 降序索引，支持按时间排序

### 2.3 HTML生成任务表 (html_generation_tasks)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | ObjectId | 主键 | MongoDB自动生成的ID |
| prompt | String | 必填 | 生成提示词 |
| imageUrl | String | 可选 | 参考图片URL |
| model | String | 必填 | 使用的AI模型 |
| status | String | 必填 | 任务状态枚举值 |
| result | String | 可选 | 生成的HTML代码 |
| error | String | 可选 | 错误信息 |
| createdAt | Date | 自动 | 创建时间 |
| updatedAt | Date | 自动 | 更新时间 |
| startedAt | Date | 可选 | 开始执行时间 |
| completedAt | Date | 可选 | 完成时间 |

**任务状态枚举:**
- `pending`: 待处理
- `running`: 执行中
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

**索引设计:**
- `status`: 普通索引，支持按状态查询
- `model`: 普通索引，支持按模型查询
- `createdAt`: 降序索引，支持按时间排序

### 2.4 提示词模板表 (prompt_templates) - 待实现

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| _id | ObjectId | 主键 | MongoDB自动生成的ID |
| name | String | 必填，唯一 | 模板名称 |
| prompt | String | 必填 | 提示词内容 |
| description | String | 可选 | 模板描述 |
| category | String | 可选 | 模板分类 |
| variables | Array | 可选 | 模板变量定义 |
| createdAt | Date | 自动 | 创建时间 |
| updatedAt | Date | 自动 | 更新时间 |

## 3. API接口设计

### 3.1 模板管理接口

#### 3.1.1 获取模板列表
```
GET /templates?page=1&limit=10
```

**查询参数:**
- `page`: 页码，默认1
- `limit`: 每页数量，默认10，最大100

**响应格式:**
```json
{
  "success": true,
  "data": {
    "data": [模板对象数组],
    "total": 总数量,
    "page": 当前页码,
    "limit": 每页数量,
    "totalPages": 总页数
  }
}
```

#### 3.1.2 创建模板
```
POST /templates
Content-Type: application/json
```

**请求体:**
```json
{
  "jsonExample": "JSON示例字符串",
  "tsDefinition": "TypeScript类型定义",
  "diagramLink": "示意图链接（可选）",
  "prompt": "生成提示词"
}
```

**业务逻辑:**
1. 验证必填字段
2. 检查tsDefinition唯一性
3. 自动生成创建时间和更新时间
4. 返回创建的模板对象

#### 3.1.3 获取单个模板
```
GET /templates/:id
```

#### 3.1.4 更新模板
```
PUT /templates/:id
Content-Type: application/json
```

#### 3.1.5 删除模板
```
DELETE /templates/:id
```

#### 3.1.6 搜索模板
```
GET /templates/search?keyword=关键词&page=1&limit=10
```

### 3.2 HTML生成任务接口

#### 3.2.1 创建生成任务
```
POST /html-tasks
Content-Type: application/json
```

**请求体:**
```json
{
  "prompt": "生成提示词",
  "imageUrl": "参考图片URL（可选）",
  "model": "AI模型名称（可选，默认gpt-4.1）"
}
```

**响应格式:**
```json
{
  "success": true,
  "data": {
    "taskId": "任务ID",
    "message": "任务已创建，正在处理中..."
  }
}
```

**业务逻辑:**
1. 验证必填字段
2. 创建任务记录，状态为pending
3. 添加到任务队列
4. 启动队列处理
5. 返回任务ID

#### 3.2.2 获取任务详情
```
GET /html-tasks/:id
```

#### 3.2.3 获取任务列表
```
GET /html-tasks?page=1&limit=10&status=pending
```

**查询参数:**
- `page`: 页码
- `limit`: 每页数量
- `status`: 任务状态过滤（可选）

#### 3.2.4 取消任务
```
DELETE /html-tasks/:id
```

**业务逻辑:**
1. 检查任务是否存在
2. 只能取消pending或running状态的任务
3. 更新任务状态为cancelled
4. 从队列中移除任务

#### 3.2.5 获取队列状态
```
GET /html-tasks-queue/status
```

**响应格式:**
```json
{
  "success": true,
  "data": {
    "queueLength": 队列长度,
    "isProcessing": 是否正在处理,
    "nextTask": 下一个任务对象或null
  }
}
```

### 3.3 生成记录管理接口

#### 3.3.1 获取生成记录列表
```
GET /generation-records?page=1&limit=10
```

#### 3.3.2 创建生成记录
```
POST /generation-records
Content-Type: application/json
```

**请求体:**
```json
{
  "templateId": "模板ID",
  "generatedResult": "生成结果",
  "modelUsed": "使用的模型"
}
```

#### 3.3.3 根据模板ID获取生成记录
```
GET /templates/:templateId/generation-records?page=1&limit=10
```

#### 3.3.4 根据模型获取生成记录
```
GET /generation-records/model/:model?page=1&limit=10
```

#### 3.3.5 获取统计信息
```
GET /generation-records/stats
```

### 3.4 提示词模板接口 (待实现)

#### 3.4.1 获取模板列表
```
GET /prompt-templates?page=1&limit=10&category=分类
```

#### 3.4.2 创建提示词模板
```
POST /prompt-templates
Content-Type: application/json
```

**请求体:**
```json
{
  "name": "模板名称",
  "prompt": "提示词内容",
  "description": "模板描述",
  "category": "模板分类",
  "variables": ["变量1", "变量2"]
}
```

### 3.5 健康检查接口

#### 3.5.1 健康检查
```
GET /health
```

**响应格式:**
```json
{
  "success": true,
  "message": "服务运行正常",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 4. 业务流程设计

### 4.1 HTML生成任务流程

```mermaid
graph TD
    A[用户提交生成请求] --> B[创建任务记录]
    B --> C[任务状态: pending]
    C --> D[添加到任务队列]
    D --> E[队列处理器检查]
    E --> F{队列是否为空?}
    F -->|否| G[取出队列头部任务]
    F -->|是| H[等待新任务]
    G --> I[更新任务状态: running]
    I --> J[调用AI服务]
    J --> K{AI调用成功?}
    K -->|是| L[提取HTML内容]
    K -->|否| M[记录错误信息]
    L --> N[更新任务状态: completed]
    M --> O[更新任务状态: failed]
    N --> P[处理下一个任务]
    O --> P
    P --> E
```

### 4.2 任务队列管理流程

#### 4.2.1 任务创建流程
1. 接收用户请求
2. 验证请求参数
3. 创建任务记录（状态：pending）
4. 添加到内存队列
5. 触发队列处理
6. 返回任务ID给用户

#### 4.2.2 任务执行流程
1. 从队列取出任务
2. 检查任务是否被取消
3. 更新任务状态为running
4. 调用AI服务生成HTML
5. 处理AI服务响应
6. 更新任务状态和结果
7. 继续处理下一个任务

#### 4.2.3 任务重试机制
- AI服务调用失败时，返回示例HTML作为fallback
- 不进行自动重试，避免资源浪费
- 用户可以手动重新创建任务

#### 4.2.4 服务重启恢复
1. 服务启动时查询数据库
2. 找到所有pending状态的任务
3. 按创建时间排序加入队列
4. 启动队列处理器

### 4.3 模板管理流程

#### 4.3.1 模板创建流程
1. 接收模板数据
2. 验证必填字段
3. 检查tsDefinition唯一性
4. 创建模板记录
5. 返回创建结果

#### 4.3.2 模板更新流程
1. 验证模板ID存在性
2. 检查tsDefinition唯一性（如果修改）
3. 更新模板记录
4. 更新updatedAt时间戳

## 5. 错误处理和异常情况

### 5.1 API错误响应格式

```json
{
  "success": false,
  "error": "错误信息",
  "code": "错误代码（可选）"
}
```

### 5.2 常见错误类型

#### 5.2.1 参数验证错误 (400)
- 必填参数缺失
- 参数格式不正确
- 参数值超出范围

#### 5.2.2 资源不存在错误 (404)
- 模板不存在
- 任务不存在
- 生成记录不存在

#### 5.2.3 业务逻辑错误 (400)
- tsDefinition重复
- 任务状态不允许操作
- 队列已满（如果有限制）

#### 5.2.4 服务器错误 (500)
- 数据库连接失败
- AI服务调用异常
- 系统内部错误

### 5.3 AI服务异常处理

#### 5.3.1 网络异常
- 连接超时
- 网络不可达
- DNS解析失败

#### 5.3.2 API异常
- 认证失败
- 配额超限
- 服务不可用

#### 5.3.3 响应异常
- 响应格式错误
- 内容为空
- 解析失败

**处理策略:**
- 记录详细错误日志
- 返回示例HTML作为fallback
- 更新任务状态为failed
- 提供错误信息给用户

## 6. 性能和扩展性考虑

### 6.1 性能优化

#### 6.1.1 数据库优化
- 合理设计索引，提高查询性能
- 使用分页查询，避免大量数据加载
- 定期清理过期数据

#### 6.1.2 任务队列优化
- 内存队列提高处理速度
- 数据库持久化保证可靠性
- 避免重复任务处理

#### 6.1.3 AI服务调用优化
- 设置合理的超时时间
- 实现连接池复用
- 缓存常用结果（可选）

### 6.2 扩展性设计

#### 6.2.1 水平扩展
- 支持多实例部署
- 共享数据库和任务队列
- 负载均衡分发请求

#### 6.2.2 功能扩展
- 支持更多AI模型
- 增加任务优先级
- 实现任务批处理

### 6.3 监控和日志

#### 6.3.1 关键指标监控
- 任务处理速度
- 成功率统计
- 队列长度监控
- AI服务响应时间

#### 6.3.2 日志记录
- 请求响应日志
- 错误详情日志
- 性能指标日志
- 业务操作日志

## 7. 安全性考虑

### 7.1 输入验证
- 严格验证所有输入参数
- 防止SQL注入和NoSQL注入
- 限制文件上传大小和类型

### 7.2 访问控制
- API访问频率限制
- 用户身份验证（可选）
- 权限管理（可选）

### 7.3 数据安全
- 敏感信息加密存储
- 定期数据备份
- 访问日志记录

## 8. 部署和运维

### 8.1 环境要求
- Deno 1.40+
- MongoDB 4.4+
- 网络访问AI服务

### 8.2 配置管理
- 环境变量配置
- 配置文件管理
- 敏感信息保护

### 8.3 健康检查
- 服务状态检查
- 数据库连接检查
- 外部服务依赖检查

## 9. 测试策略

### 9.1 单元测试
- 服务层逻辑测试
- 工具函数测试
- 错误处理测试

### 9.2 集成测试
- API接口测试
- 数据库操作测试
- 任务队列测试

### 9.3 端到端测试
- 完整业务流程测试
- 异常场景测试
- 性能压力测试

## 10. 版本规划

### 10.1 当前版本 (v1.0)
- ✅ 模板管理功能
- ✅ HTML生成任务
- ✅ 生成记录管理
- ✅ 基础任务队列

### 10.2 下一版本 (v1.1) - 待开发
- ⏳ 提示词模板管理
- ⏳ 任务优先级支持
- ⏳ 批量操作功能
- ⏳ 更完善的错误处理

### 10.3 未来版本 (v2.0)
- 🔮 用户认证和权限管理
- 🔮 多租户支持
- 🔮 实时任务状态推送
- 🔮 高级统计和分析功能

## 11. 附录

### 11.1 数据库连接配置

```typescript
// 数据库连接示例
const client = new MongoClient();
await client.connect("mongodb://localhost:27017");
const db = client.database("local_util");
```

### 11.2 AI服务配置

```typescript
// AI服务调用配置
const AI_SERVICE_CONFIG = {
  baseUrl: "https://aigc.sankuai.com/v1/openai/native/chat/completions",
  apiKey: "your-api-key",
  defaultModel: "gpt-4.1",
  timeout: 30000,
  maxTokens: 16384
};
```

### 11.3 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| PORT | 服务端口 | 8000 |
| MONGODB_URI | MongoDB连接字符串 | mongodb://localhost:27017 |
| AI_API_KEY | AI服务API密钥 | - |
| LOG_LEVEL | 日志级别 | info |

---

**文档版本**: v1.0
**最后更新**: 2024-01-01
**维护者**: 开发团队