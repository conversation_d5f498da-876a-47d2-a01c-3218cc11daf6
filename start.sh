#!/bin/bash

# 本地工具后端服务启动脚本

echo "🚀 启动本地工具后端服务..."

# 检查Deno是否安装
if ! command -v deno &> /dev/null; then
    echo "❌ Deno未安装，请先安装Deno"
    echo "安装命令: curl -fsSL https://deno.land/x/install/install.sh | sh"
    exit 1
fi

# 检查Deno版本
DENO_VERSION=$(deno --version | head -n 1 | cut -d' ' -f2)
echo "📦 Deno版本: $DENO_VERSION"

# 创建数据目录
mkdir -p data

# 启动开发服务器
echo "🔧 启动开发服务器..."
echo "📍 服务地址: http://localhost:8000"
echo "📖 API文档: 请查看 README.md"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

deno task dev 