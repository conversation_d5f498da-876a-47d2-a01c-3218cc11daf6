{"templates": [{"jsonExample": "{\"name\":\"张三\",\"age\":25}", "tsDefinition": "interface User { name: string; age: number; }", "prompt": "创建一个用户接口", "id": 1, "createdAt": "2025-07-22T13:20:19.445Z", "updatedAt": "2025-07-22T13:20:19.445Z"}], "generationRecords": [{"templateId": 1, "generatedResult": "interface User { name: string; age: number; }", "modelUsed": "gpt-4", "id": 1, "createdAt": "2025-07-22T13:20:28.672Z", "updatedAt": "2025-07-22T13:20:28.672Z"}], "nextTemplateId": 2, "nextRecordId": 2}