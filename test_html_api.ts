// HTML生成任务API测试文件
const BASE_URL = 'http://localhost:8000';

// 测试创建HTML生成任务
async function testCreateHtmlTask() {
  console.log('\n=== 测试创建HTML生成任务 ===');
  
  const taskData = {
    prompt: '创建一个简单的登录页面，包含用户名和密码输入框，以及一个登录按钮。使用现代化的设计风格。',
    model: 'gpt-4.1'
  };

  try {
    const response = await fetch(`${BASE_URL}/html-tasks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(taskData),
    });

    const result = await response.json();
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (result.success && result.data.taskId) {
      console.log('✅ 任务创建成功，任务ID:', result.data.taskId);
      return result.data.taskId;
    } else {
      console.log('❌ 任务创建失败');
      return null;
    }
  } catch (error) {
    console.error('❌ 请求失败:', error);
    return null;
  }
}

// 测试获取任务详情
async function testGetHtmlTask(taskId: string) {
  console.log('\n=== 测试获取任务详情 ===');
  
  try {
    const response = await fetch(`${BASE_URL}/html-tasks/${taskId}`);
    const result = await response.json();
    
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ 获取任务详情成功');
      console.log('任务状态:', result.data.status);
      if (result.data.result) {
        console.log('生成的HTML长度:', result.data.result.length);
      }
    } else {
      console.log('❌ 获取任务详情失败');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error);
  }
}

// 测试获取任务列表
async function testGetHtmlTasks() {
  console.log('\n=== 测试获取任务列表 ===');
  
  try {
    const response = await fetch(`${BASE_URL}/html-tasks?page=1&limit=5`);
    const result = await response.json();
    
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ 获取任务列表成功');
      console.log('任务总数:', result.data.total);
      console.log('当前页任务数:', result.data.tasks.length);
    } else {
      console.log('❌ 获取任务列表失败');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error);
  }
}

// 测试获取队列状态
async function testGetQueueStatus() {
  console.log('\n=== 测试获取队列状态 ===');
  
  try {
    const response = await fetch(`${BASE_URL}/html-tasks-queue/status`);
    const result = await response.json();
    
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ 获取队列状态成功');
      console.log('队列长度:', result.data.queueLength);
      console.log('是否正在处理:', result.data.isProcessing);
    } else {
      console.log('❌ 获取队列状态失败');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error);
  }
}

// 测试取消任务
async function testCancelHtmlTask(taskId: string) {
  console.log('\n=== 测试取消任务 ===');
  
  try {
    const response = await fetch(`${BASE_URL}/html-tasks/${taskId}`, {
      method: 'DELETE',
    });

    const result = await response.json();
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ 任务取消成功');
    } else {
      console.log('❌ 任务取消失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 请求失败:', error);
  }
}

// 测试带图片的HTML生成任务
async function testCreateHtmlTaskWithImage() {
  console.log('\n=== 测试创建带图片的HTML生成任务 ===');
  
  const taskData = {
    prompt: '根据这张图片创建一个相似的网页布局',
    imageUrl: 'https://example.com/sample-image.jpg',
    model: 'gpt-4.1'
  };

  try {
    const response = await fetch(`${BASE_URL}/html-tasks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(taskData),
    });

    const result = await response.json();
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (result.success && result.data.taskId) {
      console.log('✅ 带图片的任务创建成功，任务ID:', result.data.taskId);
      return result.data.taskId;
    } else {
      console.log('❌ 带图片的任务创建失败');
      return null;
    }
  } catch (error) {
    console.error('❌ 请求失败:', error);
    return null;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试HTML生成任务API...');

  // 测试队列状态
  await testGetQueueStatus();

  // 测试创建任务
  const taskId1 = await testCreateHtmlTask();
  
  // 测试创建带图片的任务
  const taskId2 = await testCreateHtmlTaskWithImage();

  // 等待一秒让任务开始处理
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 测试获取任务列表
  await testGetHtmlTasks();

  // 测试获取任务详情
  if (taskId1) {
    await testGetHtmlTask(taskId1);
  }

  // 测试取消任务
  if (taskId2) {
    await testCancelHtmlTask(taskId2);
  }

  // 再次检查队列状态
  await testGetQueueStatus();

  // 等待几秒查看任务执行结果
  console.log('\n⏳ 等待5秒查看任务执行结果...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  if (taskId1) {
    await testGetHtmlTask(taskId1);
  }

  console.log('\n✅ 所有测试完成！');
}

// 如果直接运行此文件，则执行测试
if (import.meta.main) {
  runTests().catch(console.error);
}

export {
  testCreateHtmlTask,
  testGetHtmlTask,
  testGetHtmlTasks,
  testGetQueueStatus,
  testCancelHtmlTask,
  testCreateHtmlTaskWithImage,
  runTests
}; 