// API测试脚本
const BASE_URL = 'http://localhost:8000';

async function testAPI() {
  console.log('🧪 开始API测试...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await fetch(`${BASE_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('健康检查结果:', healthData);
    console.log('');

    // 测试创建模板
    console.log('2. 测试创建模板...');
    const templateData = {
      jsonExample: '{"name": "张三", "age": 25, "email": "<EMAIL>"}',
      tsDefinition: 'interface User { name: string; age: number; email: string; }',
      diagramLink: 'https://example.com/user-diagram.png',
      prompt: '生成一个用户信息模板，包含姓名、年龄和邮箱'
    };

    const createTemplateResponse = await fetch(`${BASE_URL}/templates`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(templateData)
    });
    const createdTemplate = await createTemplateResponse.json();
    console.log('创建模板结果:', createdTemplate);
    console.log('');

    if (createdTemplate.success && createdTemplate.data) {
      const templateId = createdTemplate.data.id;

      // 测试获取模板列表
      console.log('3. 测试获取模板列表...');
      const templatesResponse = await fetch(`${BASE_URL}/templates?page=1&limit=10`);
      const templatesData = await templatesResponse.json();
      console.log('模板列表结果:', templatesData);
      console.log('');

      // 测试创建生成记录
      console.log('4. 测试创建生成记录...');
      const recordData = {
        templateId: templateId,
        generatedResult: '根据模板生成的用户信息：张三，25岁，邮箱：<EMAIL>',
        modelUsed: 'gpt-4'
      };

      const createRecordResponse = await fetch(`${BASE_URL}/generation-records`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(recordData)
      });
      const createdRecord = await createRecordResponse.json();
      console.log('创建生成记录结果:', createdRecord);
      console.log('');

      if (createdRecord.success && createdRecord.data) {
        const recordId = createdRecord.data.id;

        // 测试获取生成记录列表
        console.log('5. 测试获取生成记录列表...');
        const recordsResponse = await fetch(`${BASE_URL}/generation-records?page=1&limit=10`);
        const recordsData = await recordsResponse.json();
        console.log('生成记录列表结果:', recordsData);
        console.log('');

        // 测试获取统计信息
        console.log('6. 测试获取统计信息...');
        const statsResponse = await fetch(`${BASE_URL}/generation-records/stats`);
        const statsData = await statsResponse.json();
        console.log('统计信息结果:', statsData);
        console.log('');

        // 测试根据模板ID获取生成记录
        console.log('7. 测试根据模板ID获取生成记录...');
        const templateRecordsResponse = await fetch(`${BASE_URL}/templates/${templateId}/generation-records?page=1&limit=10`);
        const templateRecordsData = await templateRecordsResponse.json();
        console.log('模板生成记录结果:', templateRecordsData);
        console.log('');

        // 测试搜索模板
        console.log('8. 测试搜索模板...');
        const searchResponse = await fetch(`${BASE_URL}/templates/search?keyword=用户&page=1&limit=10`);
        const searchData = await searchResponse.json();
        console.log('搜索模板结果:', searchData);
        console.log('');

        // 清理测试数据
        console.log('9. 清理测试数据...');
        await fetch(`${BASE_URL}/generation-records/${recordId}`, { method: 'DELETE' });
        await fetch(`${BASE_URL}/templates/${templateId}`, { method: 'DELETE' });
        console.log('测试数据清理完成');
      }
    }

    console.log('\n✅ API测试完成！');

  } catch (error) {
    console.error('❌ API测试失败:', error);
  }
}

// 运行测试
testAPI(); 