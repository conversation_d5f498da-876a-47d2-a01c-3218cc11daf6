# 本地工具后端服务

基于 Deno 和 Oak 框架构建的后端服务，提供模板管理和生成记录的 CRUD 功能。

## 功能特性

### 模板管理 (Templates)
- **JSON示例**: 存储JSON格式的示例数据
- **TS格式定义**: TypeScript类型定义（唯一约束）
- **示意图链接**: 可选的示意图链接
- **提示词**: 用于生成的相关提示词

### 生成记录管理 (Generation Records)
- **关联模板**: 关联到模板表的ID
- **生成结果**: 存储生成的具体结果
- **所用模型**: 记录使用的AI模型

## 技术栈

- **运行时**: Deno
- **Web框架**: Oak
- **数据库**: MongoDB
- **CORS**: oak_cors

## 快速开始

### 环境要求

- Deno 1.40+ 

### 安装和运行

1. 进入项目目录
```bash
cd back_end
```

2. 启动开发服务器
```bash
deno task dev
```

3. 启动生产服务器
```bash
deno task start
```

### 环境变量

- `PORT`: 服务器端口（默认: 8000）

## API 接口

### 模板管理

#### 获取所有模板
```
GET /templates?page=1&limit=10
```

#### 创建模板
```
POST /templates
Content-Type: application/json

{
  "jsonExample": "{\"name\": \"示例\"}",
  "tsDefinition": "interface Example { name: string; }",
  "diagramLink": "https://example.com/diagram.png",
  "prompt": "生成一个用户信息模板"
}
```

#### 获取单个模板
```
GET /templates/:id
```

#### 更新模板
```
PUT /templates/:id
Content-Type: application/json

{
  "jsonExample": "{\"name\": \"新示例\"}",
  "prompt": "更新后的提示词"
}
```

#### 删除模板
```
DELETE /templates/:id
```

#### 搜索模板
```
GET /templates/search?keyword=用户&page=1&limit=10
```

### 生成记录管理

#### 获取所有生成记录
```
GET /generation-records?page=1&limit=10
```

#### 创建生成记录
```
POST /generation-records
Content-Type: application/json

{
  "templateId": 1,
  "generatedResult": "生成的具体结果",
  "modelUsed": "gpt-4"
}
```

#### 获取单个生成记录
```
GET /generation-records/:id
```

#### 更新生成记录
```
PUT /generation-records/:id
Content-Type: application/json

{
  "generatedResult": "更新后的结果",
  "modelUsed": "gpt-3.5-turbo"
}
```

#### 删除生成记录
```
DELETE /generation-records/:id
```

#### 根据模板ID获取生成记录
```
GET /templates/:templateId/generation-records?page=1&limit=10
```

#### 根据模型获取生成记录
```
GET /generation-records/model/:model?page=1&limit=10
```

#### 获取统计信息
```
GET /generation-records/stats
```

### 健康检查

```
GET /health
```

## 数据库结构

### templates 表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键，自增 |
| jsonExample | TEXT | JSON示例（必填） |
| tsDefinition | TEXT | TS格式定义（必填，唯一） |
| diagramLink | TEXT | 示意图链接（可选） |
| prompt | TEXT | 提示词（必填） |
| createdAt | DATETIME | 创建时间 |
| updatedAt | DATETIME | 更新时间 |

### generation_records 表
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键，自增 |
| templateId | INTEGER | 关联模板ID（外键） |
| generatedResult | TEXT | 生成结果（必填） |
| modelUsed | TEXT | 所用模型（必填） |
| createdAt | DATETIME | 创建时间 |
| updatedAt | DATETIME | 更新时间 |

## 响应格式

所有API响应都遵循统一的格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "error": "错误信息"
}
```

分页响应格式：

```json
{
  "success": true,
  "data": {
    "data": [],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

## 开发

### 项目结构

```
back_end/
├── src/
│   ├── main.ts                 # 应用入口
│   ├── types/
│   │   └── index.ts           # 类型定义
│   ├── database/
│   │   ├── connection.ts      # 数据库连接
│   │   └── schema.ts          # 数据库表结构
│   ├── services/
│   │   ├── templateService.ts      # 模板业务逻辑
│   │   └── generationRecordService.ts  # 生成记录业务逻辑
│   ├── controllers/
│   │   ├── templateController.ts      # 模板控制器
│   │   └── generationRecordController.ts  # 生成记录控制器
│   └── routes/
│       └── index.ts           # 路由配置
├── deno.json                  # Deno配置
└── README.md                  # 项目说明
```

### 可用命令

```bash
# 开发模式（带热重载）
deno task dev

# 生产模式
deno task start

# 运行测试
deno task test

# 代码格式化
deno fmt

# 代码检查
deno lint
```

## 许可证

MIT 