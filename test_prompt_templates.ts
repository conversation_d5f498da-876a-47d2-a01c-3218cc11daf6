#!/usr/bin/env -S deno run --allow-net

/**
 * 提示词模板API测试脚本
 * 用于测试新增的提示词模板功能
 */

const BASE_URL = 'http://localhost:8000';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

interface PromptTemplate {
  _id?: string;
  name: string;
  prompt: string;
  description?: string;
  category?: string;
  variables?: string[];
  createdAt?: string;
  updatedAt?: string;
}

// 测试数据
const testTemplates = [
  {
    name: 'HTML页面生成',
    prompt: '请根据以下要求生成一个完整的HTML页面：\n- 响应式设计\n- 现代化样式\n- 语义化标签\n\n具体要求：{requirements}',
    description: '用于生成完整HTML页面的模板',
    category: 'HTML',
    variables: ['requirements']
  },
  {
    name: 'React组件生成',
    prompt: '请生成一个React函数组件：\n组件名：{componentName}\n功能描述：{description}\n\n要求：\n- 使用TypeScript\n- 包含适当的props类型定义\n- 添加必要的注释',
    description: '用于生成React组件的模板',
    category: 'React',
    variables: ['componentName', 'description']
  },
  {
    name: 'CSS样式生成',
    prompt: '请为以下元素生成CSS样式：\n元素类型：{elementType}\n样式要求：{styleRequirements}\n\n请使用现代CSS特性，确保浏览器兼容性。',
    description: '用于生成CSS样式的模板',
    category: 'CSS',
    variables: ['elementType', 'styleRequirements']
  }
];

async function makeRequest<T>(method: string, endpoint: string, data?: any): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    const result = await response.json();
    return result;
  } catch (error) {
    console.error(`请求失败: ${method} ${endpoint}`, error);
    return { success: false, error: error.message };
  }
}

async function testCreateTemplates() {
  console.log('\n🧪 测试创建提示词模板...');
  
  const createdTemplates: PromptTemplate[] = [];
  
  for (const template of testTemplates) {
    console.log(`创建模板: ${template.name}`);
    const result = await makeRequest<PromptTemplate>('POST', '/prompt-templates', template);
    
    if (result.success && result.data) {
      console.log(`✅ 创建成功: ${result.data._id}`);
      createdTemplates.push(result.data);
    } else {
      console.log(`❌ 创建失败: ${result.error}`);
    }
  }
  
  return createdTemplates;
}

async function testGetTemplates() {
  console.log('\n🧪 测试获取提示词模板列表...');
  
  const result = await makeRequest<any>('GET', '/prompt-templates?page=1&limit=10');
  
  if (result.success && result.data) {
    console.log(`✅ 获取成功，共 ${result.data.total} 个模板`);
    console.log(`页码: ${result.data.page}/${result.data.totalPages}`);
    
    result.data.data.forEach((template: PromptTemplate, index: number) => {
      console.log(`  ${index + 1}. ${template.name} (${template.category || '无分类'})`);
    });
  } else {
    console.log(`❌ 获取失败: ${result.error}`);
  }
  
  return result.data?.data || [];
}

async function testGetTemplateById(templateId: string) {
  console.log(`\n🧪 测试获取单个模板 (ID: ${templateId})...`);
  
  const result = await makeRequest<PromptTemplate>('GET', `/prompt-templates/${templateId}`);
  
  if (result.success && result.data) {
    console.log(`✅ 获取成功: ${result.data.name}`);
    console.log(`  描述: ${result.data.description}`);
    console.log(`  分类: ${result.data.category}`);
    console.log(`  变量: ${result.data.variables?.join(', ') || '无'}`);
  } else {
    console.log(`❌ 获取失败: ${result.error}`);
  }
  
  return result.data;
}

async function testUpdateTemplate(templateId: string) {
  console.log(`\n🧪 测试更新模板 (ID: ${templateId})...`);
  
  const updateData = {
    description: '更新后的描述 - 用于生成高质量HTML页面的专业模板',
    category: 'Web开发'
  };
  
  const result = await makeRequest<PromptTemplate>('PUT', `/prompt-templates/${templateId}`, updateData);
  
  if (result.success && result.data) {
    console.log(`✅ 更新成功`);
    console.log(`  新描述: ${result.data.description}`);
    console.log(`  新分类: ${result.data.category}`);
  } else {
    console.log(`❌ 更新失败: ${result.error}`);
  }
  
  return result.data;
}

async function testSearchTemplates() {
  console.log('\n🧪 测试搜索提示词模板...');
  
  const keyword = 'HTML';
  const result = await makeRequest<any>('GET', `/prompt-templates/search?keyword=${keyword}&page=1&limit=5`);
  
  if (result.success && result.data) {
    console.log(`✅ 搜索成功，关键词 "${keyword}" 找到 ${result.data.total} 个结果`);
    
    result.data.data.forEach((template: PromptTemplate, index: number) => {
      console.log(`  ${index + 1}. ${template.name}`);
    });
  } else {
    console.log(`❌ 搜索失败: ${result.error}`);
  }
}

async function testGetCategories() {
  console.log('\n🧪 测试获取分类列表...');
  
  const result = await makeRequest<string[]>('GET', '/prompt-templates/categories');
  
  if (result.success && result.data) {
    console.log(`✅ 获取成功，共 ${result.data.length} 个分类:`);
    result.data.forEach((category, index) => {
      console.log(`  ${index + 1}. ${category}`);
    });
  } else {
    console.log(`❌ 获取失败: ${result.error}`);
  }
}

async function testGetCategoryStats() {
  console.log('\n🧪 测试获取分类统计...');
  
  const result = await makeRequest<{ category: string; count: number }[]>('GET', '/prompt-templates/stats/categories');
  
  if (result.success && result.data) {
    console.log(`✅ 获取成功:`);
    result.data.forEach((stat, index) => {
      console.log(`  ${index + 1}. ${stat.category}: ${stat.count} 个模板`);
    });
  } else {
    console.log(`❌ 获取失败: ${result.error}`);
  }
}

async function testDeleteTemplate(templateId: string) {
  console.log(`\n🧪 测试删除模板 (ID: ${templateId})...`);
  
  const result = await makeRequest<any>('DELETE', `/prompt-templates/${templateId}`);
  
  if (result.success) {
    console.log(`✅ 删除成功`);
  } else {
    console.log(`❌ 删除失败: ${result.error}`);
  }
}

async function main() {
  console.log('🚀 开始测试提示词模板API...');
  console.log(`服务地址: ${BASE_URL}`);
  
  try {
    // 1. 创建测试模板
    const createdTemplates = await testCreateTemplates();
    
    if (createdTemplates.length === 0) {
      console.log('❌ 没有成功创建任何模板，停止测试');
      return;
    }
    
    // 2. 获取模板列表
    const templates = await testGetTemplates();
    
    // 3. 获取单个模板
    if (createdTemplates.length > 0) {
      await testGetTemplateById(createdTemplates[0]._id!);
    }
    
    // 4. 更新模板
    if (createdTemplates.length > 0) {
      await testUpdateTemplate(createdTemplates[0]._id!);
    }
    
    // 5. 搜索模板
    await testSearchTemplates();
    
    // 6. 获取分类列表
    await testGetCategories();
    
    // 7. 获取分类统计
    await testGetCategoryStats();
    
    // 8. 删除测试模板（清理）
    console.log('\n🧹 清理测试数据...');
    for (const template of createdTemplates) {
      if (template._id) {
        await testDeleteTemplate(template._id);
      }
    }
    
    console.log('\n✅ 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

if (import.meta.main) {
  main();
}
