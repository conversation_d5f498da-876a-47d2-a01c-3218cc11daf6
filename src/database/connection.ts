import { MongoClient, Database } from 'mongo';

const MONGO_URL = Deno.env.get('MONGO_URL') || 'mongodb://localhost:27017';
const DB_NAME = Deno.env.get('DB_NAME') || 'local_util';

let client: MongoClient;
let db: Database;

export async function connectToDatabase() {
  try {
    client = new MongoClient();
    await client.connect(MONGO_URL);
    db = client.database(DB_NAME);
    console.log('✅ MongoDB连接成功');
    return db;
  } catch (error) {
    console.error('❌ MongoDB连接失败:', error);
    throw error;
  }
}

export function getDatabase(): Database {
  if (!db) {
    throw new Error('数据库未连接，请先调用 connectToDatabase()');
  }
  return db;
}

export async function closeDatabase() {
  if (client) {
    await client.close();
    console.log('🔌 MongoDB连接已关闭');
  }
} 