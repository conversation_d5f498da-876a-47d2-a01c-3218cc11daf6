import { Database } from 'mongo';
import {
  Template,
  GenerationRecord,
  HtmlGenerationTask,
  PromptTemplate
} from '../types/index.ts';

// 注意：这里的接口定义已经移动到 types/index.ts 中，保持一致性

export async function initCollections(db: Database) {
  // 创建模板集合
  const templatesCollection = db.collection<Template>('templates');

  // 创建生成记录集合
  const generationRecordsCollection = db.collection<GenerationRecord>('generation_records');

  // 创建HTML生成任务集合
  const htmlTasksCollection = db.collection<HtmlGenerationTask>('html_generation_tasks');

  // 创建提示词模板集合
  const promptTemplatesCollection = db.collection<PromptTemplate>('prompt_templates');
  
  // 创建索引
  await templatesCollection.createIndexes({
    indexes: [
      {
        key: { tsDefinition: 1 },
        name: "tsDefinition_unique",
        unique: true
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      }
    ]
  });
  
  await generationRecordsCollection.createIndexes({
    indexes: [
      {
        key: { templateId: 1 },
        name: "templateId_index"
      },
      {
        key: { modelUsed: 1 },
        name: "modelUsed_index"
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      }
    ]
  });

  await htmlTasksCollection.createIndexes({
    indexes: [
      {
        key: { status: 1 },
        name: "status_index"
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      },
      {
        key: { model: 1 },
        name: "model_index"
      }
    ]
  });

  // 为提示词模板集合创建索引
  await promptTemplatesCollection.createIndexes({
    indexes: [
      {
        key: { name: 1 },
        name: "name_unique",
        unique: true
      },
      {
        key: { category: 1 },
        name: "category_index"
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      }
    ]
  });

  console.log('✅ MongoDB集合和索引初始化完成');

  return {
    templates: templatesCollection,
    generationRecords: generationRecordsCollection,
    htmlTasks: htmlTasksCollection,
    promptTemplates: promptTemplatesCollection
  };
}