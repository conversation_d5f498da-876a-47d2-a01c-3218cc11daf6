import { Database } from 'mongo';
import { HtmlGenerationTask, TaskStatus } from '../types/index.ts';

export interface Template {
  _id?: string;
  jsonExample: string;
  tsDefinition: string;
  diagramLink?: string;
  prompt: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface GenerationRecord {
  _id?: string;
  templateId: string;
  generatedResult: string;
  modelUsed: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function initCollections(db: Database) {
  // 创建模板集合
  const templatesCollection = db.collection<Template>('templates');
  
  // 创建生成记录集合
  const generationRecordsCollection = db.collection<GenerationRecord>('generation_records');
  
  // 创建HTML生成任务集合
  const htmlTasksCollection = db.collection<HtmlGenerationTask>('html_generation_tasks');
  
  // 创建索引
  await templatesCollection.createIndexes({
    indexes: [
      {
        key: { tsDefinition: 1 },
        name: "tsDefinition_unique",
        unique: true
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      }
    ]
  });
  
  await generationRecordsCollection.createIndexes({
    indexes: [
      {
        key: { templateId: 1 },
        name: "templateId_index"
      },
      {
        key: { modelUsed: 1 },
        name: "modelUsed_index"
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      }
    ]
  });

  await htmlTasksCollection.createIndexes({
    indexes: [
      {
        key: { status: 1 },
        name: "status_index"
      },
      {
        key: { createdAt: -1 },
        name: "createdAt_desc"
      },
      {
        key: { model: 1 },
        name: "model_index"
      }
    ]
  });
  
  console.log('✅ MongoDB集合和索引初始化完成');
  
  return {
    templates: templatesCollection,
    generationRecords: generationRecordsCollection,
    htmlTasks: htmlTasksCollection
  };
} 