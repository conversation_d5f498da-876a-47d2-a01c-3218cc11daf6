// 第一张表：模板表
export interface Template {
  id?: number;
  jsonExample: string; // JSON示例
  tsDefinition: string; // TS格式定义（unique）
  diagramLink?: string; // 示意图链接（可选）
  prompt: string; // 提示词
  createdAt?: string;
  updatedAt?: string;
}

// 第二张表：生成记录表
export interface GenerationRecord {
  id?: number;
  templateId: number; // 关联第一张表的id
  generatedResult: string; // 生成结果
  modelUsed: string; // 所用模型
  createdAt?: string;
  updatedAt?: string;
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',    // 待处理
  RUNNING = 'running',    // 执行中
  COMPLETED = 'completed', // 已完成
  FAILED = 'failed',      // 失败
  CANCELLED = 'cancelled' // 已取消
}

// HTML生成任务表
export interface HtmlGenerationTask {
  _id?: string;
  prompt: string;           // 生成提示词
  imageUrl?: string;        // 可选的图片URL
  model: string;           // 使用的AI模型
  status: TaskStatus;      // 任务状态
  result?: string;         // 生成的HTML代码
  error?: string;          // 错误信息（如果失败）
  createdAt: Date;         // 创建时间
  updatedAt: Date;         // 更新时间
  startedAt?: Date;        // 开始执行时间
  completedAt?: Date;      // 完成时间
}

// 创建任务请求
export interface CreateHtmlTaskRequest {
  prompt: string;
  imageUrl?: string;
  model?: string;
}

// AI模型相关类型
export interface AIModelMessage {
  role: 'user' | 'assistant' | 'system';
  content: string | Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

export interface AIModelRequestConfig {
  model: string;
  messages: AIModelMessage[];
  stream: boolean;
  max_tokens: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页参数
export interface PaginationParams {
  page: number;
  limit: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
} 