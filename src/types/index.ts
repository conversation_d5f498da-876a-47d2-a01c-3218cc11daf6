// 模板表 - 与PRD和MongoDB实现一致
export interface Template {
  _id?: string; // MongoDB ObjectId
  jsonExample: string; // JSON示例数据
  tsDefinition: string; // TypeScript类型定义（唯一约束）
  diagramLink?: string; // 示意图链接URL（可选）
  prompt: string; // 生成提示词
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间
}

// 生成记录表 - 与PRD和MongoDB实现一致
export interface GenerationRecord {
  _id?: string; // MongoDB ObjectId
  templateId: string; // 关联模板表的ID
  generatedResult: string; // 生成的结果内容
  modelUsed: string; // 使用的AI模型名称
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',    // 待处理
  RUNNING = 'running',    // 执行中
  COMPLETED = 'completed', // 已完成
  FAILED = 'failed',      // 失败
  CANCELLED = 'cancelled' // 已取消
}

// HTML生成任务表 - 与PRD一致
export interface HtmlGenerationTask {
  _id?: string; // MongoDB ObjectId
  prompt: string; // 生成提示词
  imageUrl?: string; // 参考图片URL（可选）
  model: string; // 使用的AI模型
  status: TaskStatus; // 任务状态
  result?: string; // 生成的HTML代码（可选）
  error?: string; // 错误信息（可选）
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间
  startedAt?: Date; // 开始执行时间（可选）
  completedAt?: Date; // 完成时间（可选）
}

// 提示词模板表 - 根据PRD新增
export interface PromptTemplate {
  _id?: string; // MongoDB ObjectId
  name: string; // 模板名称（唯一）
  prompt: string; // 提示词内容
  description?: string; // 模板描述（可选）
  category?: string; // 模板分类（可选）
  variables?: string[]; // 模板变量定义（可选）
  createdAt: Date; // 创建时间
  updatedAt: Date; // 更新时间
}

// 创建HTML任务请求
export interface CreateHtmlTaskRequest {
  prompt: string;
  imageUrl?: string;
  model?: string;
}

// 创建提示词模板请求
export interface CreatePromptTemplateRequest {
  name: string;
  prompt: string;
  description?: string;
  category?: string;
  variables?: string[];
}

// 更新提示词模板请求
export interface UpdatePromptTemplateRequest {
  name?: string;
  prompt?: string;
  description?: string;
  category?: string;
  variables?: string[];
}

// AI模型相关类型
export interface AIModelMessage {
  role: 'user' | 'assistant' | 'system';
  content: string | Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
}

export interface AIModelRequestConfig {
  model: string;
  messages: AIModelMessage[];
  stream: boolean;
  max_tokens: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页参数
export interface PaginationParams {
  page: number;
  limit: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
} 