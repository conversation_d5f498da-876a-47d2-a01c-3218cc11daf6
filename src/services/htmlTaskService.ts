import { Collection } from 'https://deno.land/x/mongo@v0.32.0/mod.ts';
import { getDatabase } from '../database/connection.ts';
import { HtmlGenerationTask, TaskStatus, CreateHtmlTaskRequest, AIModelMessage, AIModelRequestConfig } from '../types/index.ts';

class HtmlTaskService {
  private htmlTasksCollection: Collection<HtmlGenerationTask> | null = null;
  private taskQueue: HtmlGenerationTask[] = [];
  private isProcessing = false;

  constructor() {
    // 延迟初始化，在第一次使用时连接数据库
  }

  private getCollection(): Collection<HtmlGenerationTask> {
    if (!this.htmlTasksCollection) {
      this.htmlTasksCollection = getDatabase().collection<HtmlGenerationTask>('html_generation_tasks');
      // 首次初始化时恢复未完成的任务到队列
      this.restorePendingTasks();
    }
    return this.htmlTasksCollection;
  }

  // 创建新任务
  async createTask(taskData: CreateHtmlTaskRequest): Promise<string> {
    const task: HtmlGenerationTask = {
      prompt: taskData.prompt,
      imageUrl: taskData.imageUrl,
      model: taskData.model || 'gpt-4.1',
      status: TaskStatus.PENDING,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await this.getCollection().insertOne(task);
    const taskId = result.toString();
    
    // 添加到任务队列
    const taskWithId = { ...task, _id: taskId };
    this.taskQueue.push(taskWithId);
    
    // 启动任务处理
    this.processQueue();
    
    return taskId;
  }

  // 获取任务详情
  async getTaskById(taskId: string): Promise<HtmlGenerationTask | null> {
    const result = await this.getCollection().findOne({ _id: taskId });
    return result || null;
  }

  // 获取任务列表
  async getTasks(page = 1, limit = 10, status?: TaskStatus): Promise<{
    tasks: HtmlGenerationTask[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const filter = status ? { status } : {};
    const skip = (page - 1) * limit;
    
    const tasks = await this.getCollection()
      .find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await this.getCollection().countDocuments(filter);

    return {
      tasks,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 取消任务
  async cancelTask(taskId: string): Promise<boolean> {
    const task = await this.getCollection().findOne({ _id: taskId });
    
    if (!task) {
      return false;
    }

    // 只能取消待处理或执行中的任务
    if (task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) {
      await this.getCollection().updateOne(
        { _id: taskId },
        {
          $set: {
            status: TaskStatus.CANCELLED,
            updatedAt: new Date(),
            completedAt: new Date()
          }
        }
      );

      // 从队列中移除
      this.taskQueue = this.taskQueue.filter(t => t._id !== taskId);
      
      return true;
    }

    return false;
  }

  // 恢复待处理的任务到队列
  private async restorePendingTasks() {
    const pendingTasks = await this.getCollection()
      .find({ status: TaskStatus.PENDING })
      .sort({ createdAt: 1 })
      .toArray();
    
    this.taskQueue = pendingTasks;
    
    if (this.taskQueue.length > 0) {
      console.log(`恢复了 ${this.taskQueue.length} 个待处理任务到队列`);
      this.processQueue();
    }
  }

  // 处理任务队列
  private async processQueue() {
    if (this.isProcessing || this.taskQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.taskQueue.length > 0) {
      const task = this.taskQueue.shift();
      if (!task || !task._id) continue;

      // 检查任务是否已被取消
      const currentTask = await this.getCollection().findOne({ _id: task._id });
      if (!currentTask || currentTask.status === TaskStatus.CANCELLED) {
        continue;
      }

      await this.executeTask(task);
    }

    this.isProcessing = false;
  }

  // 执行单个任务
  private async executeTask(task: HtmlGenerationTask) {
    if (!task._id) return;

    try {
      // 更新任务状态为执行中
      await this.getCollection().updateOne(
        { _id: task._id },
        {
          $set: {
            status: TaskStatus.RUNNING,
            startedAt: new Date(),
            updatedAt: new Date()
          }
        }
      );

      // 调用AI服务生成HTML
      const htmlResult = await this.callAIService(task.prompt, task.imageUrl, task.model);

      // 更新任务为完成状态
      await this.getCollection().updateOne(
        { _id: task._id },
        {
          $set: {
            status: TaskStatus.COMPLETED,
            result: htmlResult,
            completedAt: new Date(),
            updatedAt: new Date()
          }
        }
      );

      console.log(`任务 ${task._id} 执行完成`);

    } catch (error) {
      console.error(`任务 ${task._id} 执行失败:`, error);
      
      const errorMessage = error instanceof Error ? error.message : '任务执行失败';
      
      // 更新任务为失败状态
      await this.getCollection().updateOne(
        { _id: task._id },
        {
          $set: {
            status: TaskStatus.FAILED,
            error: errorMessage,
            completedAt: new Date(),
            updatedAt: new Date()
          }
        }
      );
    }
  }

  // AI服务调用
  private async callAIService(prompt: string, imageUrl?: string, model = 'gpt-4.1'): Promise<string> {
    try {
      // 构建请求消息
      let messageContent: AIModelMessage['content'];

      // 如果有图片URL，使用数组格式
      if (imageUrl) {
        messageContent = [
          {
            type: 'image_url',
            image_url: {
              url: imageUrl,
            },
          },
          {
            type: 'text',
            text: prompt,
          },
        ];
      } else {
        // 如果没有图片，直接使用字符串格式
        messageContent = prompt;
      }

      // 构建请求配置
      const requestConfig: AIModelRequestConfig = {
        model: model,
        messages: [
          {
            role: 'user',
            content: messageContent,
          },
        ],
        stream: false,
        max_tokens: 16384,
      };

      // 发送请求
      const response = await fetch('https://aigc.sankuai.com/v1/openai/native/chat/completions', {
        method: 'POST',
        headers: {
          Authorization: 'Bearer 21909898402213769267',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestConfig),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // 处理非流式响应
      const responseData = await response.json();
      console.log('AI服务响应:', responseData);

      if (!responseData.choices || !responseData.choices[0]) {
        throw new Error('API响应格式错误：缺少choices字段');
      }

      const choice = responseData.choices[0];
      let htmlContent = '';

      // 获取完整的content
      if (choice.message && choice.message.content) {
        htmlContent = choice.message.content;
      } else {
        throw new Error('API响应格式错误：缺少message.content字段');
      }

      if (!htmlContent.trim()) {
        throw new Error('AI服务返回空内容');
      }

      // 从markdown代码块中提取HTML内容
      const extractedHtml = this.extractHtmlFromMarkdown(htmlContent);

      return extractedHtml;
    } catch (error) {
      console.error('调用AI服务失败:', error);

      // 如果AI服务调用失败，返回示例HTML作为fallback
      console.warn('AI服务调用失败，使用示例HTML作为fallback');
      return this.generateSampleHtml();
    }
  }

  // 从markdown代码块中提取HTML内容
  private extractHtmlFromMarkdown(content: string): string {
    // 匹配 ```html 或 ```HTML 代码块
    const htmlCodeBlockRegex = /```(?:html|HTML)\s*([\s\S]*?)\s*```/i;
    const match = content.match(htmlCodeBlockRegex);
    
    if (match && match[1]) {
      return match[1].trim();
    }

    // 如果没有找到HTML代码块，尝试匹配普通的```代码块
    const codeBlockRegex = /```\s*([\s\S]*?)\s*```/;
    const generalMatch = content.match(codeBlockRegex);
    
    if (generalMatch && generalMatch[1]) {
      return generalMatch[1].trim();
    }

    // 如果都没有找到，返回原内容
    return content;
  }

  // 生成示例HTML
  private generateSampleHtml(): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        p {
            line-height: 1.6;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI服务暂时不可用</h1>
        <p>这是一个示例HTML页面，当AI服务不可用时返回。请检查网络连接或稍后重试。</p>
    </div>
</body>
</html>`;
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      queueLength: this.taskQueue.length,
      isProcessing: this.isProcessing,
      nextTask: this.taskQueue.length > 0 ? this.taskQueue[0] : null
    };
  }
}

export default new HtmlTaskService(); 