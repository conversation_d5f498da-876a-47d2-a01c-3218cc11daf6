import { getDatabase } from '../database/connection.ts';
import { Template } from '../database/schema.ts';
import { PaginationParams, PaginatedResponse } from '../types/index.ts';

export class TemplateService {
  // 创建模板
  static async create(template: Omit<Template, '_id' | 'createdAt' | 'updatedAt'>): Promise<Template> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    
    const now = new Date();
    const newTemplate: Template = {
      ...template,
      createdAt: now,
      updatedAt: now
    };
    
    const result = await collection.insertOne(newTemplate);
    return { ...newTemplate, _id: result.toString() };
  }

  // 获取所有模板（分页）
  static async findAll(params: PaginationParams): Promise<PaginatedResponse<Template>> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    const templates = await collection.find({})
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments({});

    return {
      data: templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 根据ID获取模板
  static async findById(id: string): Promise<Template | null> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    
    return await collection.findOne({ _id: id });
  }

  // 根据TS定义获取模板
  static async findByTsDefinition(tsDefinition: string): Promise<Template | null> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    
    return await collection.findOne({ tsDefinition });
  }

  // 更新模板
  static async update(id: string, template: Partial<Omit<Template, '_id' | 'createdAt' | 'updatedAt'>>): Promise<Template | null> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    
    const updateData = {
      ...template,
      updatedAt: new Date()
    };
    
    const result = await collection.updateOne(
      { _id: id },
      { $set: updateData }
    );
    
    if (result.modifiedCount === 0) {
      return null;
    }
    
    return await this.findById(id);
  }

  // 删除模板
  static async delete(id: string): Promise<boolean> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    
    const result = await collection.deleteOne({ _id: id });
    return result.deletedCount > 0;
  }

  // 搜索模板
  static async search(keyword: string, params: PaginationParams): Promise<PaginatedResponse<Template>> {
    const db = getDatabase();
    const collection = db.collection<Template>('templates');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    const searchQuery = {
      $or: [
        { jsonExample: { $regex: keyword, $options: 'i' } },
        { tsDefinition: { $regex: keyword, $options: 'i' } },
        { prompt: { $regex: keyword, $options: 'i' } }
      ]
    };

    const templates = await collection.find(searchQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments(searchQuery);

    return {
      data: templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }
} 