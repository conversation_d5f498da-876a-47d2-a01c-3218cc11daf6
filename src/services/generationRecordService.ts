import { getDatabase } from '../database/connection.ts';
import { GenerationRecord } from '../database/schema.ts';
import { PaginationParams, PaginatedResponse } from '../types/index.ts';

export class GenerationRecordService {
  // 创建生成记录
  static async create(record: Omit<GenerationRecord, '_id' | 'createdAt' | 'updatedAt'>): Promise<GenerationRecord> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    
    const now = new Date();
    const newRecord: GenerationRecord = {
      ...record,
      createdAt: now,
      updatedAt: now
    };
    
    const result = await collection.insertOne(newRecord);
    return { ...newRecord, _id: result.toString() };
  }

  // 获取所有生成记录（分页）
  static async findAll(params: PaginationParams): Promise<PaginatedResponse<GenerationRecord>> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    const records = await collection.find({})
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments({});

    return {
      data: records,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 根据ID获取生成记录
  static async findById(id: string): Promise<GenerationRecord | null> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    
    return await collection.findOne({ _id: id });
  }

  // 根据模板ID获取生成记录
  static async findByTemplateId(templateId: string, params: PaginationParams): Promise<PaginatedResponse<GenerationRecord>> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    const records = await collection.find({ templateId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments({ templateId });

    return {
      data: records,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 更新生成记录
  static async update(id: string, record: Partial<Omit<GenerationRecord, '_id' | 'createdAt' | 'updatedAt'>>): Promise<GenerationRecord | null> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    
    const updateData = {
      ...record,
      updatedAt: new Date()
    };
    
    const result = await collection.updateOne(
      { _id: id },
      { $set: updateData }
    );
    
    if (result.modifiedCount === 0) {
      return null;
    }
    
    return await this.findById(id);
  }

  // 删除生成记录
  static async delete(id: string): Promise<boolean> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    
    const result = await collection.deleteOne({ _id: id });
    return result.deletedCount > 0;
  }

  // 根据模型获取生成记录
  static async findByModel(modelUsed: string, params: PaginationParams): Promise<PaginatedResponse<GenerationRecord>> {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    const records = await collection.find({ modelUsed })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments({ modelUsed });

    return {
      data: records,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取统计信息
  static async getStats() {
    const db = getDatabase();
    const collection = db.collection<GenerationRecord>('generation_records');
    
    const totalRecords = await collection.countDocuments({});
    
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentRecords = await collection.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });
    
    return {
      totalRecords,
      recentRecords
    };
  }
} 