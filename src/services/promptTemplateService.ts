import { getDatabase } from '../database/connection.ts';
import { 
  PromptTemplate, 
  CreatePromptTemplateRequest, 
  UpdatePromptTemplateRequest,
  PaginationParams, 
  PaginatedResponse 
} from '../types/index.ts';

export class PromptTemplateService {
  // 创建提示词模板
  static async create(templateData: CreatePromptTemplateRequest): Promise<PromptTemplate> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');
    
    const now = new Date();
    const newTemplate: PromptTemplate = {
      ...templateData,
      createdAt: now,
      updatedAt: now
    };
    
    const result = await collection.insertOne(newTemplate);
    return { ...newTemplate, _id: result.toString() };
  }

  // 获取所有提示词模板（分页）
  static async findAll(params: PaginationParams, category?: string): Promise<PaginatedResponse<PromptTemplate>> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const filter = category ? { category } : {};

    const templates = await collection.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments(filter);

    return {
      data: templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 根据ID获取提示词模板
  static async findById(id: string): Promise<PromptTemplate | null> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');

    const result = await collection.findOne({ _id: id });
    return result || null;
  }

  // 根据名称获取提示词模板
  static async findByName(name: string): Promise<PromptTemplate | null> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');

    const result = await collection.findOne({ name });
    return result || null;
  }

  // 更新提示词模板
  static async update(id: string, templateData: UpdatePromptTemplateRequest): Promise<PromptTemplate | null> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');
    
    const updateData = {
      ...templateData,
      updatedAt: new Date()
    };
    
    const result = await collection.updateOne(
      { _id: id },
      { $set: updateData }
    );
    
    if (result.modifiedCount === 0) {
      return null;
    }
    
    return await this.findById(id);
  }

  // 删除提示词模板
  static async delete(id: string): Promise<boolean> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');

    const result = await collection.deleteOne({ _id: id });
    return result > 0;
  }

  // 搜索提示词模板
  static async search(keyword: string, params: PaginationParams): Promise<PaginatedResponse<PromptTemplate>> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');
    const { page, limit } = params;
    const skip = (page - 1) * limit;

    const searchQuery = {
      $or: [
        { name: { $regex: keyword, $options: 'i' } },
        { prompt: { $regex: keyword, $options: 'i' } },
        { description: { $regex: keyword, $options: 'i' } },
        { category: { $regex: keyword, $options: 'i' } }
      ]
    };

    const templates = await collection.find(searchQuery)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    const total = await collection.countDocuments(searchQuery);

    return {
      data: templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // 获取所有分类
  static async getCategories(): Promise<string[]> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');

    const categories = await collection.distinct('category', { category: { $exists: true, $ne: '' } });
    return categories.filter((cat: unknown) => cat && typeof cat === 'string' && cat.trim() !== '') as string[];
  }

  // 根据分类获取模板数量统计
  static async getCategoryStats(): Promise<{ category: string; count: number }[]> {
    const db = getDatabase();
    const collection = db.collection<PromptTemplate>('prompt_templates');
    
    const pipeline = [
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      },
      {
        $match: {
          _id: { $ne: null }
        }
      },
      {
        $project: {
          category: '$_id',
          count: 1,
          _id: 0
        }
      },
      {
        $sort: { count: -1 }
      }
    ];
    
    const result = await collection.aggregate(pipeline).toArray();
    return result as unknown as { category: string; count: number }[];
  }
}
