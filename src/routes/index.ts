import { Router } from 'https://deno.land/x/oak@v12.6.1/mod.ts';
import { TemplateController } from '../controllers/templateController.ts';
import { GenerationRecordController } from '../controllers/generationRecordController.ts';
import htmlTaskController from '../controllers/htmlTaskController.ts';

const router = new Router();

// 模板相关路由
router
  .get('/templates', TemplateController.findAll)
  .post('/templates', TemplateController.create)
  .get('/templates/search', TemplateController.search)
  .get('/templates/:id', TemplateController.findById)
  .put('/templates/:id', TemplateController.update)
  .delete('/templates/:id', TemplateController.delete);

// 生成记录相关路由
router
  .get('/generation-records', GenerationRecordController.findAll)
  .post('/generation-records', GenerationRecordController.create)
  .get('/generation-records/stats', GenerationRecordController.getStats)
  .get('/generation-records/:id', GenerationRecordController.findById)
  .put('/generation-records/:id', GenerationRecordController.update)
  .delete('/generation-records/:id', GenerationRecordController.delete)
  .get('/templates/:templateId/generation-records', GenerationRecordController.findByTemplateId)
  .get('/generation-records/model/:model', GenerationRecordController.findByModel);

// HTML生成任务相关路由
router
  .post('/html-tasks', htmlTaskController.createTask.bind(htmlTaskController))
  .get('/html-tasks', htmlTaskController.getTasks.bind(htmlTaskController))
  .get('/html-tasks/:id', htmlTaskController.getTask.bind(htmlTaskController))
  .delete('/html-tasks/:id', htmlTaskController.cancelTask.bind(htmlTaskController))
  .get('/html-tasks-queue/status', htmlTaskController.getQueueStatus.bind(htmlTaskController));

// 健康检查
router.get('/health', (ctx) => {
  ctx.response.body = {
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString()
  };
});

export default router; 