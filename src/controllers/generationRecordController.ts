import { Context } from 'https://deno.land/x/oak@v12.6.1/mod.ts';

interface ContextWithParams extends Context {
  params: Record<string, string>;
}
import { GenerationRecordService } from '../services/generationRecordService.ts';
import { TemplateService } from '../services/templateService.ts';
import { ApiResponse, PaginationParams } from '../types/index.ts';

export class GenerationRecordController {
  // 创建生成记录
  static async create(ctx: Context) {
    try {
      const body = await ctx.request.body().value;
      
      if (!body.templateId || !body.generatedResult || !body.modelUsed) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '缺少必要字段：templateId, generatedResult, modelUsed'
        } as ApiResponse<null>;
        return;
      }

      // 验证模板是否存在
      const template = await TemplateService.findById(body.templateId);
      if (!template) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '关联的模板不存在'
        } as ApiResponse<null>;
        return;
      }

      const record = await GenerationRecordService.create({
        templateId: body.templateId,
        generatedResult: body.generatedResult,
        modelUsed: body.modelUsed
      });

      ctx.response.status = 201;
      ctx.response.body = {
        success: true,
        data: record
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 获取所有生成记录
  static async findAll(ctx: Context) {
    try {
      const url = new URL(ctx.request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      
      const params: PaginationParams = { page, limit };
      const result = await GenerationRecordService.findAll(params);

      ctx.response.body = {
        success: true,
        data: result
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 根据ID获取生成记录
  static async findById(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的ID'
        } as ApiResponse<null>;
        return;
      }

      const record = await GenerationRecordService.findById(id);
      
      if (!record) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '生成记录不存在'
        } as ApiResponse<null>;
        return;
      }

      ctx.response.body = {
        success: true,
        data: record
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 根据模板ID获取生成记录
  static async findByTemplateId(ctx: ContextWithParams) {
    try {
      const templateId = ctx.params.templateId;
      
      if (!templateId) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的模板ID'
        } as ApiResponse<null>;
        return;
      }

      const url = new URL(ctx.request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      
      const params: PaginationParams = { page, limit };
      const result = await GenerationRecordService.findByTemplateId(templateId, params);

      ctx.response.body = {
        success: true,
        data: result
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 更新生成记录
  static async update(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的ID'
        } as ApiResponse<null>;
        return;
      }

      const body = await ctx.request.body().value;
      
      // 如果要更新模板ID，验证模板是否存在
      if (body.templateId) {
        const template = await TemplateService.findById(body.templateId);
        if (!template) {
          ctx.response.status = 404;
          ctx.response.body = {
            success: false,
            error: '关联的模板不存在'
          } as ApiResponse<null>;
          return;
        }
      }

      const record = await GenerationRecordService.update(id, body);
      
      if (!record) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '生成记录不存在'
        } as ApiResponse<null>;
        return;
      }

      ctx.response.body = {
        success: true,
        data: record
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 删除生成记录
  static async delete(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的ID'
        } as ApiResponse<null>;
        return;
      }

      const success = await GenerationRecordService.delete(id);
      
      if (!success) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '生成记录不存在'
        } as ApiResponse<null>;
        return;
      }

      ctx.response.body = {
        success: true,
        message: '生成记录删除成功'
      } as ApiResponse<null>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 根据模型获取生成记录
  static async findByModel(ctx: ContextWithParams) {
    try {
      const modelUsed = ctx.params.model;
      
      if (!modelUsed) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '模型名称不能为空'
        } as ApiResponse<null>;
        return;
      }

      const url = new URL(ctx.request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      
      const params: PaginationParams = { page, limit };
      const result = await GenerationRecordService.findByModel(modelUsed, params);

      ctx.response.body = {
        success: true,
        data: result
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 获取统计信息
  static async getStats(ctx: Context) {
    try {
      const stats = await GenerationRecordService.getStats();

      ctx.response.body = {
        success: true,
        data: stats
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }
} 