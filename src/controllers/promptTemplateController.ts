import { Context } from 'https://deno.land/x/oak@v12.6.1/mod.ts';
import { PromptTemplateService } from '../services/promptTemplateService.ts';
import { CreatePromptTemplateRequest, UpdatePromptTemplateRequest } from '../types/index.ts';

interface ContextWithParams extends Context {
  params: Record<string, string>;
}

export class PromptTemplateController {
  // 获取所有提示词模板
  static async findAll(ctx: Context) {
    try {
      const url = new URL(ctx.request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      const category = url.searchParams.get('category') || undefined;

      if (page < 1 || limit < 1 || limit > 100) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '页码必须大于0，每页数量必须在1-100之间'
        };
        return;
      }

      const result = await PromptTemplateService.findAll({ page, limit }, category);

      ctx.response.body = {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('获取提示词模板列表失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取提示词模板列表失败'
      };
    }
  }

  // 创建提示词模板
  static async create(ctx: Context) {
    try {
      const body = await ctx.request.body().value;
      const templateData: CreatePromptTemplateRequest = JSON.parse(body);

      // 验证必填字段
      if (!templateData.name || !templateData.prompt) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '模板名称和提示词内容是必填字段'
        };
        return;
      }

      // 检查名称是否已存在
      const existingTemplate = await PromptTemplateService.findByName(templateData.name);
      if (existingTemplate) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '模板名称已存在'
        };
        return;
      }

      const template = await PromptTemplateService.create(templateData);

      ctx.response.status = 201;
      ctx.response.body = {
        success: true,
        data: template
      };
    } catch (error) {
      console.error('创建提示词模板失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '创建提示词模板失败'
      };
    }
  }

  // 根据ID获取提示词模板
  static async findById(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '模板ID不能为空'
        };
        return;
      }

      const template = await PromptTemplateService.findById(id);
      
      if (!template) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '提示词模板不存在'
        };
        return;
      }

      ctx.response.body = {
        success: true,
        data: template
      };
    } catch (error) {
      console.error('获取提示词模板失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取提示词模板失败'
      };
    }
  }

  // 更新提示词模板
  static async update(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      const body = await ctx.request.body().value;
      const updateData: UpdatePromptTemplateRequest = JSON.parse(body);

      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '模板ID不能为空'
        };
        return;
      }

      // 如果更新名称，检查是否与其他模板重复
      if (updateData.name) {
        const existingTemplate = await PromptTemplateService.findByName(updateData.name);
        if (existingTemplate && existingTemplate._id !== id) {
          ctx.response.status = 400;
          ctx.response.body = {
            success: false,
            error: '模板名称已存在'
          };
          return;
        }
      }

      const template = await PromptTemplateService.update(id, updateData);
      
      if (!template) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '提示词模板不存在'
        };
        return;
      }

      ctx.response.body = {
        success: true,
        data: template
      };
    } catch (error) {
      console.error('更新提示词模板失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '更新提示词模板失败'
      };
    }
  }

  // 删除提示词模板
  static async delete(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '模板ID不能为空'
        };
        return;
      }

      const success = await PromptTemplateService.delete(id);
      
      if (!success) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '提示词模板不存在'
        };
        return;
      }

      ctx.response.body = {
        success: true,
        data: {
          message: '提示词模板删除成功'
        }
      };
    } catch (error) {
      console.error('删除提示词模板失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '删除提示词模板失败'
      };
    }
  }

  // 搜索提示词模板
  static async search(ctx: Context) {
    try {
      const url = new URL(ctx.request.url);
      const keyword = url.searchParams.get('keyword');
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');

      if (!keyword) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '搜索关键词不能为空'
        };
        return;
      }

      if (page < 1 || limit < 1 || limit > 100) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '页码必须大于0，每页数量必须在1-100之间'
        };
        return;
      }

      const result = await PromptTemplateService.search(keyword, { page, limit });

      ctx.response.body = {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('搜索提示词模板失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '搜索提示词模板失败'
      };
    }
  }

  // 获取所有分类
  static async getCategories(ctx: Context) {
    try {
      const categories = await PromptTemplateService.getCategories();

      ctx.response.body = {
        success: true,
        data: categories
      };
    } catch (error) {
      console.error('获取分类列表失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取分类列表失败'
      };
    }
  }

  // 获取分类统计
  static async getCategoryStats(ctx: Context) {
    try {
      const stats = await PromptTemplateService.getCategoryStats();

      ctx.response.body = {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('获取分类统计失败:', error);
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: '获取分类统计失败'
      };
    }
  }
}
