import { Context } from 'https://deno.land/x/oak@v12.6.1/mod.ts';
import { TemplateService } from '../services/templateService.ts';
import { ApiResponse, PaginationParams } from '../types/index.ts';

interface ContextWithParams extends Context {
  params: Record<string, string>;
}

export class TemplateController {
  // 创建模板
  static async create(ctx: Context) {
    try {
      const body = await ctx.request.body().value;
      
      if (!body.jsonExample || !body.tsDefinition || !body.prompt) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '缺少必要字段：jsonExample, tsDefinition, prompt'
        } as ApiResponse<null>;
        return;
      }

      // 检查TS定义是否已存在
      const existing = await TemplateService.findByTsDefinition(body.tsDefinition);
      if (existing) {
        ctx.response.status = 409;
        ctx.response.body = {
          success: false,
          error: 'TS定义已存在'
        } as ApiResponse<null>;
        return;
      }

      const template = await TemplateService.create({
        jsonExample: body.jsonExample,
        tsDefinition: body.tsDefinition,
        diagramLink: body.diagramLink,
        prompt: body.prompt
      });

      ctx.response.status = 201;
      ctx.response.body = {
        success: true,
        data: template
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 获取所有模板
  static async findAll(ctx: Context) {
    try {
      const url = new URL(ctx.request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      
      const params: PaginationParams = { page, limit };
      const result = await TemplateService.findAll(params);

      ctx.response.body = {
        success: true,
        data: result
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 根据ID获取模板
  static async findById(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的ID'
        } as ApiResponse<null>;
        return;
      }

      const template = await TemplateService.findById(id);
      
      if (!template) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '模板不存在'
        } as ApiResponse<null>;
        return;
      }

      ctx.response.body = {
        success: true,
        data: template
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 更新模板
  static async update(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的ID'
        } as ApiResponse<null>;
        return;
      }

      const body = await ctx.request.body().value;
      
      // 如果要更新TS定义，检查是否与其他记录冲突
      if (body.tsDefinition) {
        const existing = await TemplateService.findByTsDefinition(body.tsDefinition);
        if (existing && existing._id !== id) {
          ctx.response.status = 409;
          ctx.response.body = {
            success: false,
            error: 'TS定义已存在'
          } as ApiResponse<null>;
          return;
        }
      }

      const template = await TemplateService.update(id, body);
      
      if (!template) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '模板不存在'
        } as ApiResponse<null>;
        return;
      }

      ctx.response.body = {
        success: true,
        data: template
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 删除模板
  static async delete(ctx: ContextWithParams) {
    try {
      const id = ctx.params.id;
      
      if (!id) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '无效的ID'
        } as ApiResponse<null>;
        return;
      }

      const success = await TemplateService.delete(id);
      
      if (!success) {
        ctx.response.status = 404;
        ctx.response.body = {
          success: false,
          error: '模板不存在'
        } as ApiResponse<null>;
        return;
      }

      ctx.response.body = {
        success: true,
        message: '模板删除成功'
      } as ApiResponse<null>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }

  // 搜索模板
  static async search(ctx: Context) {
    try {
      const url = new URL(ctx.request.url);
      const keyword = url.searchParams.get('keyword') || '';
      const page = parseInt(url.searchParams.get('page') || '1');
      const limit = parseInt(url.searchParams.get('limit') || '10');
      
      if (!keyword.trim()) {
        ctx.response.status = 400;
        ctx.response.body = {
          success: false,
          error: '搜索关键词不能为空'
        } as ApiResponse<null>;
        return;
      }

      const params: PaginationParams = { page, limit };
      const result = await TemplateService.search(keyword, params);

      ctx.response.body = {
        success: true,
        data: result
      } as ApiResponse<any>;
    } catch (error) {
      ctx.response.status = 500;
      ctx.response.body = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      } as ApiResponse<null>;
    }
  }
} 