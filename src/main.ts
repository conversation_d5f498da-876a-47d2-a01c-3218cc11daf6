import { Application } from 'https://deno.land/x/oak@v12.6.1/mod.ts';
import { CORS } from 'https://deno.land/x/oak_cors@v0.1.1/mod.ts';
import router from './routes/index.ts';
import { connectToDatabase, closeDatabase, getDatabase } from './database/connection.ts';
import { initCollections } from './database/schema.ts';

const app = new Application();
const PORT = Deno.env.get('PORT') || 8000;

// 初始化数据库连接
await connectToDatabase();
await initCollections(getDatabase());

// 启用CORS
app.use(CORS({
  origin: ['http://localhost:3000', 'http://localhost:5173', 'http://127.0.0.1:3000', 'http://127.0.0.1:5173'],
  credentials: true,
}));

// 请求日志中间件
app.use(async (ctx, next) => {
  const start = Date.now();
  await next();
  const ms = Date.now() - start;
  console.log(`${ctx.request.method} ${ctx.request.url.pathname} - ${ms}ms`);
});

// 错误处理中间件
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (err) {
    console.error('服务器错误:', err);
    ctx.response.status = 500;
    ctx.response.body = {
      success: false,
      error: '服务器内部错误'
    };
  }
});

// 使用路由
app.use(router.routes());
app.use(router.allowedMethods());

// 404处理
app.use((ctx) => {
  ctx.response.status = 404;
  ctx.response.body = {
    success: false,
    error: '接口不存在'
  };
});

// 优雅关闭
Deno.addSignalListener('SIGINT', async () => {
  console.log('\n正在关闭服务器...');
  await closeDatabase();
  Deno.exit(0);
});

Deno.addSignalListener('SIGTERM', async () => {
  console.log('\n正在关闭服务器...');
  await closeDatabase();
  Deno.exit(0);
});

console.log(`🚀 服务器启动在 http://localhost:${PORT}`);
await app.listen({ port: typeof PORT === 'string' ? parseInt(PORT) : PORT }); 