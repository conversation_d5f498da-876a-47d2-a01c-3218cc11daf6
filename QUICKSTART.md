# 快速开始指南

## 🚀 5分钟快速启动

### 1. 环境准备

确保已安装 Deno 1.40+：

```bash
# 检查Deno版本
deno --version

# 如果未安装，请先安装Deno
curl -fsSL https://deno.land/x/install/install.sh | sh
```

### 2. 启动服务

```bash
# 进入后端目录
cd back_end

# 使用启动脚本（推荐）
./start.sh

# 或者直接使用deno命令
deno task dev
```

### 3. 导入示例数据

在新的终端窗口中运行：

```bash
cd back_end
deno task import-data
```

### 4. 测试API

服务启动后，可以访问以下地址：

- **健康检查**: http://localhost:8000/health
- **API文档**: 查看 README.md

## 📋 API快速测试

### 创建模板

```bash
curl -X POST http://localhost:8000/templates \
  -H "Content-Type: application/json" \
  -d '{
    "jsonExample": "{\"name\": \"测试用户\", \"age\": 30}",
    "tsDefinition": "interface TestUser { name: string; age: number; }",
    "prompt": "生成一个测试用户模板"
  }'
```

### 获取模板列表

```bash
curl http://localhost:8000/templates?page=1&limit=10
```

### 创建生成记录

```bash
curl -X POST http://localhost:8000/generation-records \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": 1,
    "generatedResult": "生成的测试结果",
    "modelUsed": "gpt-4"
  }'
```

### 获取生成记录

```bash
curl http://localhost:8000/generation-records?page=1&limit=10
```

## 🔧 常用命令

```bash
# 开发模式（带热重载）
deno task dev

# 生产模式
deno task start

# 导入示例数据
deno task import-data

# 运行测试
deno task test

# 代码格式化
deno fmt

# 代码检查
deno lint
```

## 📁 项目结构

```
back_end/
├── src/
│   ├── main.ts                 # 应用入口
│   ├── types/                  # 类型定义
│   ├── database/               # 数据库相关
│   ├── services/               # 业务逻辑
│   ├── controllers/            # 控制器
│   └── routes/                 # 路由配置
├── data/                       # 数据库文件目录
├── examples/                   # 示例数据
├── deno.json                   # Deno配置
├── start.sh                    # 启动脚本
└── README.md                   # 详细文档
```

## 🐛 常见问题

### 1. 端口被占用

如果8000端口被占用，可以设置环境变量：

```bash
PORT=8001 deno task dev
```

### 2. 数据库权限问题

确保data目录有写入权限：

```bash
mkdir -p data
chmod 755 data
```

### 3. CORS问题

如果前端无法访问API，检查CORS配置是否正确。

## 📞 获取帮助

- 查看完整文档：`README.md`
- 检查API接口：访问 `http://localhost:8000/health`
- 查看日志：服务启动时会显示详细日志

---

🎉 现在您可以开始使用本地工具后端服务了！ 