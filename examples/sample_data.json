{"templates": [{"jsonExample": "{\"name\": \"张三\", \"age\": 25, \"email\": \"<EMAIL>\", \"department\": \"技术部\"}", "tsDefinition": "interface Employee { name: string; age: number; email: string; department: string; }", "diagramLink": "https://example.com/employee-diagram.png", "prompt": "生成一个员工信息模板，包含姓名、年龄、邮箱和部门信息"}, {"jsonExample": "{\"title\": \"项目A\", \"description\": \"这是一个示例项目\", \"status\": \"进行中\", \"priority\": \"高\", \"assignee\": \"李四\"}", "tsDefinition": "interface Project { title: string; description: string; status: '待开始' | '进行中' | '已完成'; priority: '低' | '中' | '高'; assignee: string; }", "diagramLink": "https://example.com/project-diagram.png", "prompt": "生成一个项目管理模板，包含项目标题、描述、状态、优先级和负责人"}, {"jsonExample": "{\"productName\": \"笔记本电脑\", \"price\": 5999, \"category\": \"电子产品\", \"inStock\": true, \"tags\": [\"高性能\", \"轻薄\", \"长续航\"]}", "tsDefinition": "interface Product { productName: string; price: number; category: string; inStock: boolean; tags: string[]; }", "diagramLink": "https://example.com/product-diagram.png", "prompt": "生成一个产品信息模板，包含产品名称、价格、分类、库存状态和标签"}], "generationRecords": [{"templateId": 1, "generatedResult": "根据员工模板生成的员工信息：王五，30岁，邮箱：<EMAIL>，部门：市场部", "modelUsed": "gpt-4"}, {"templateId": 2, "generatedResult": "根据项目模板生成的项目信息：项目B，客户管理系统开发，状态：进行中，优先级：高，负责人：赵六", "modelUsed": "gpt-3.5-turbo"}, {"templateId": 3, "generatedResult": "根据产品模板生成的产品信息：智能手机，价格：3999元，分类：电子产品，库存：有货，标签：5G、拍照、快充", "modelUsed": "gpt-4"}]}